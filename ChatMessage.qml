import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Item {
    id: root

    property bool isUserMessage: false
    property string messageText: ""
    property string timeStamp: ""

    height: messageContainer.height + 30

    RowLayout {
        id: messageContainer
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.topMargin: 15
        anchors.leftMargin: 10
        anchors.rightMargin: 10
        spacing: 15
        
        // 用户消息：右对齐，bot消息：左对齐
        Item {
            Layout.fillWidth: !isUserMessage
            Layout.preferredWidth: isUserMessage ? parent.width * 0.3 : 0
        }
        
        // 头像
        Rectangle {
            Layout.preferredWidth: 40
            Layout.preferredHeight: 40
            Layout.alignment: Qt.AlignTop
            radius: 20
            color: isUserMessage ? "#4CAF50" : "#2196F3"
            
            Text {
                anchors.centerIn: parent
                text: isUserMessage ? "U" : "AI"
                color: "white"
                font.pixelSize: 16
                font.weight: Font.Bold
            }
        }
        
        // 消息内容
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredWidth: parent.width * 0.7
            Layout.minimumHeight: messageContent.height + 40
            color: isUserMessage ? "#f0f9ff" : "#f8f9fa"
            border.color: isUserMessage ? "#e0f2fe" : "#e9ecef"
            border.width: 1
            radius: 12

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 18
                spacing: 12
                
                // 消息文本 - 支持基本的markdown渲染
                ScrollView {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.min(messageContent.contentHeight + 10, 400)
                    
                    TextArea {
                        id: messageContent
                        text: formatMarkdown(messageText)
                        textFormat: Text.RichText
                        wrapMode: TextArea.Wrap
                        readOnly: true
                        selectByMouse: true
                        font.pixelSize: 14
                        font.family: "Segoe UI, Arial, sans-serif"
                        color: "#333"
                        
                        background: Rectangle {
                            color: "transparent"
                        }
                        
                        // 处理链接点击
                        onLinkActivated: function(link) {
                            Qt.openUrlExternally(link)
                        }
                    }
                }
                
                // 时间戳
                Text {
                    Layout.alignment: isUserMessage ? Qt.AlignRight : Qt.AlignLeft
                    text: timeStamp
                    font.pixelSize: 12
                    color: "#888"
                }
            }
        }
        
        // 用户消息：左侧空白，bot消息：右侧空白
        Item {
            Layout.fillWidth: isUserMessage
            Layout.preferredWidth: !isUserMessage ? parent.width * 0.3 : 0
        }
    }
    
    // 简单的markdown格式化函数
    function formatMarkdown(text) {
        if (!text) return ""
        
        let formatted = text
        
        // 代码块 ```code```
        formatted = formatted.replace(/```([\s\S]*?)```/g, 
            '<div style="background-color: #f6f8fa; border: 1px solid #e1e4e8; border-radius: 6px; padding: 16px; margin: 8px 0; font-family: \'Consolas\', \'Monaco\', monospace; white-space: pre-wrap;">$1</div>')
        
        // 行内代码 `code`
        formatted = formatted.replace(/`([^`]+)`/g, 
            '<span style="background-color: #f6f8fa; border: 1px solid #e1e4e8; border-radius: 3px; padding: 2px 4px; font-family: \'Consolas\', \'Monaco\', monospace;">$1</span>')
        
        // 粗体 **text**
        formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>')
        
        // 斜体 *text*
        formatted = formatted.replace(/\*(.*?)\*/g, '<i>$1</i>')
        
        // 链接 [text](url)
        formatted = formatted.replace(/\[([^\]]+)\]\(([^)]+)\)/g, 
            '<a href="$2" style="color: #0366d6; text-decoration: none;">$1</a>')
        
        // 标题 # ## ###
        formatted = formatted.replace(/^### (.*$)/gm, '<h3 style="margin: 16px 0 8px 0; font-size: 18px; font-weight: 600;">$1</h3>')
        formatted = formatted.replace(/^## (.*$)/gm, '<h2 style="margin: 20px 0 10px 0; font-size: 20px; font-weight: 600;">$1</h2>')
        formatted = formatted.replace(/^# (.*$)/gm, '<h1 style="margin: 24px 0 12px 0; font-size: 24px; font-weight: 600;">$1</h1>')
        
        // 列表项 - 或 *
        formatted = formatted.replace(/^[\-\*] (.*$)/gm, '<li style="margin: 4px 0;">$1</li>')
        
        // 换行
        formatted = formatted.replace(/\n/g, '<br>')
        
        // 包装列表项
        formatted = formatted.replace(/(<li[^>]*>.*<\/li>)/g, '<ul style="margin: 8px 0; padding-left: 20px;">$1</ul>')
        
        return formatted
    }
}
