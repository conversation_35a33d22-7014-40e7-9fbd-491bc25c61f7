import QtQuick 2.15
import QtWebEngine

Item {
    id: root
    
    property string markdownText: ""
    property bool isReady: false
    
    // 当 markdown 文本改变时重新渲染
    onMarkdownTextChanged: {
        if (isReady) {
            renderMarkdown()
        }
    }
    
    WebEngineView {
        id: webView
        anchors.fill: parent
        
        // 加载本地 HTML 文件
        url: Qt.resolvedUrl("markdown-renderer.html")
        
        // 禁用上下文菜单
        settings.showScrollBars: false
        settings.focusOnNavigationEnabled: false
        
        // 页面加载完成后的回调
        onLoadingChanged: function(loadRequest) {
            if (loadRequest.status === WebEngineView.LoadSucceededStatus) {
                isReady = true
                renderMarkdown()
            }
        }
        
        // JavaScript 执行完成的回调
        onJavaScriptConsoleMessage: function(level, message, lineNumber, sourceID) {
            if (level === WebEngineView.ErrorMessageLevel) {
                console.error("WebEngine JS Error:", message, "at line", lineNumber)
            }
        }
    }
    
    // 渲染 markdown 内容
    function renderMarkdown() {
        if (!isReady) return
        
        const escapedText = markdownText.replace(/\\/g, '\\\\')
                                      .replace(/'/g, "\\'")
                                      .replace(/\n/g, '\\n')
                                      .replace(/\r/g, '\\r')
        
        const script = `window.setMarkdownContent('${escapedText}');`
        
        webView.runJavaScript(script, function(result) {
            // 渲染完成后的回调
        })
    }
    
    // 获取渲染后的高度
    function getContentHeight(callback) {
        if (!isReady) {
            callback(30)
            return
        }
        
        const script = `
            const content = document.getElementById('content');
            Math.max(content.scrollHeight + 24, 30);
        `
        
        webView.runJavaScript(script, function(result) {
            callback(result || 30)
        })
    }
}
