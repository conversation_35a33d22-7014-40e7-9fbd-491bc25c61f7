<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Renderer</title>
    
    <!-- 引入 markdown-it -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@14.0.0/dist/markdown-it.min.js"></script>
    
    <!-- 引入 highlight.js 用于代码高亮 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/highlight.min.js"></script>
    
    <!-- 引入 KaTeX 用于数学公式渲染 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 12px;
            background: transparent;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        /* 代码块样式 */
        pre {
            background-color: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            margin: 8px 0;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.45;
        }
        
        /* 行内代码样式 */
        code {
            background-color: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 3px;
            padding: 2px 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
        }
        
        pre code {
            background: transparent;
            border: none;
            padding: 0;
        }
        
        /* 表格样式 */
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }
        
        th, td {
            border: 1px solid #e1e4e8;
            padding: 8px 12px;
            text-align: left;
        }
        
        th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        
        /* 引用样式 */
        blockquote {
            border-left: 4px solid #e1e4e8;
            padding-left: 16px;
            margin: 16px 0;
            color: #666;
            background-color: #f8f9fa;
            padding: 8px 16px;
            border-radius: 0 4px 4px 0;
        }
        
        /* 链接样式 */
        a {
            color: #0366d6;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        /* 标题样式 */
        h1, h2, h3, h4, h5, h6 {
            margin: 16px 0 8px 0;
            font-weight: 600;
            line-height: 1.25;
        }
        
        h1 { font-size: 24px; }
        h2 { font-size: 20px; }
        h3 { font-size: 18px; }
        h4 { font-size: 16px; }
        h5 { font-size: 14px; }
        h6 { font-size: 13px; }
        
        /* 列表样式 */
        ul, ol {
            margin: 8px 0;
            padding-left: 20px;
        }
        
        li {
            margin: 4px 0;
        }
        
        /* 分隔线样式 */
        hr {
            border: none;
            border-top: 1px solid #e1e4e8;
            margin: 24px 0;
        }
        
        /* 数学公式样式 */
        .katex {
            font-size: 1em;
        }
        
        /* 确保内容不会超出容器 */
        * {
            max-width: 100%;
            box-sizing: border-box;
        }
        
        img {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div id="content"></div>
    
    <script>
        // 初始化 markdown-it
        const md = window.markdownit({
            html: true,
            linkify: true,
            typographer: true,
            highlight: function (str, lang) {
                if (lang && hljs.getLanguage(lang)) {
                    try {
                        return hljs.highlight(str, { language: lang }).value;
                    } catch (__) {}
                }
                return '';
            }
        });
        
        // 渲染 markdown 内容
        function renderMarkdown(markdownText) {
            try {
                const html = md.render(markdownText || '');
                document.getElementById('content').innerHTML = html;
                
                // 高亮所有代码块
                document.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightElement(block);
                });
                
                // 渲染数学公式（如果有 KaTeX）
                if (window.katex) {
                    document.querySelectorAll('code').forEach((element) => {
                        const text = element.textContent;
                        if (text.startsWith('$') && text.endsWith('$') && text.length > 2) {
                            try {
                                const formula = text.slice(1, -1);
                                katex.render(formula, element, {
                                    throwOnError: false,
                                    displayMode: false
                                });
                            } catch (e) {
                                // 如果不是有效的数学公式，保持原样
                            }
                        }
                    });
                }
                
                // 调整高度
                adjustHeight();
            } catch (error) {
                console.error('Markdown rendering error:', error);
                document.getElementById('content').textContent = markdownText || '';
            }
        }
        
        // 调整高度以适应内容
        function adjustHeight() {
            const content = document.getElementById('content');
            const height = Math.max(content.scrollHeight, 30);
            
            // 通知 QML 调整高度
            if (window.qt && window.qt.webChannelTransport) {
                // 使用 Qt WebChannel 通信
            } else {
                // 直接设置 body 高度
                document.body.style.height = height + 'px';
            }
        }
        
        // 暴露给 QML 的接口
        window.setMarkdownContent = renderMarkdown;
        
        // 初始化时渲染空内容
        renderMarkdown('');
    </script>
</body>
</html>
