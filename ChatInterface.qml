import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: root

    color: "#f5f5f5"

    RowLayout {
        anchors.fill: parent
        spacing: 0

        // 左侧边栏
        Rectangle {
            Layout.fillHeight: true
            Layout.preferredWidth: 250
            border.color: "#e0e0e0"
            border.width: 1
            color: "#f0f0f0"

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 10

                // 顶部按钮区域
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 10

                    Button {
                        Layout.preferredWidth: 50
                        flat: true
                        text: "配置"
                    }
                    Button {
                        Layout.preferredWidth: 50
                        flat: true
                        text: "试验"

                        background: Rectangle {
                            border.color: "#4CAF50"
                            border.width: 1
                            color: "#e8f5e8"
                            radius: 4
                        }
                    }
                    Button {
                        Layout.preferredWidth: 50
                        flat: true
                        text: "设置"
                    }
                    Item {
                        Layout.fillWidth: true
                    }
                    Button {
                        Layout.preferredHeight: 30
                        Layout.preferredWidth: 30
                        text: "+"
                    }
                }

                // 新建聊天按钮
                Button {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 40
                    text: "+ 新建聊天"

                    background: Rectangle {
                        border.color: "#d0d0d0"
                        border.width: 1
                        color: parent.pressed ? "#e0e0e0" : "#f8f8f8"
                        radius: 6
                    }
                }

                // 聊天列表
                ScrollView {
                    Layout.fillHeight: true
                    Layout.fillWidth: true

                    ListView {
                        spacing: 5

                        delegate: Rectangle {
                            border.color: "#e0e0e0"
                            border.width: 1
                            color: "#f8f8f8"
                            height: 50
                            radius: 6
                            width: ListView.view.width

                            Text {
                                anchors.left: parent.left
                                anchors.leftMargin: 15
                                anchors.verticalCenter: parent.verticalCenter
                                color: "#333"
                                text: model.title
                            }
                            MouseArea {
                                anchors.fill: parent
                                hoverEnabled: true

                                onEntered: parent.color = "#e8e8e8"
                                onExited: parent.color = "#f8f8f8"
                            }
                        }
                        model: ListModel {
                            ListElement {
                                title: "默认话题"
                            }
                            ListElement {
                                title: "默认话题"
                            }
                            ListElement {
                                title: "默认话题"
                            }
                        }
                    }
                }
            }
        }

        // 主聊天区域
        Rectangle {
            Layout.fillHeight: true
            Layout.fillWidth: true
            color: "white"

            ColumnLayout {
                anchors.fill: parent
                spacing: 0

                // 顶部标题栏
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 60
                    border.color: "#e0e0e0"
                    border.width: 1
                    color: "white"

                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 15

                        Image {
                            Layout.preferredHeight: 24
                            Layout.preferredWidth: 24
                            source: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIGZpbGw9IiM0Q0FGNTASCZ8L2NpcmNsZT4KPHN2Zz4K"
                        }
                        Text {
                            color: "#333"
                            font.pixelSize: 16
                            font.weight: Font.Medium
                            text: "DeepSeek Chat | 深度求索"
                        }
                        Rectangle {
                            Layout.preferredHeight: 20
                            Layout.preferredWidth: 1
                            color: "#e0e0e0"
                        }
                        Text {
                            color: "#666"
                            font.pixelSize: 14
                            text: "×"
                        }
                        Item {
                            Layout.fillWidth: true
                        }
                        Button {
                            Layout.preferredHeight: 30
                            Layout.preferredWidth: 30
                            flat: true
                            text: "🔍"
                        }
                        Button {
                            Layout.preferredHeight: 30
                            Layout.preferredWidth: 30
                            flat: true
                            text: "⚙"
                        }
                    }
                }

                // 聊天内容区域
                Rectangle {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    color: "white"

                    ScrollView {
                        anchors.fill: parent
                        anchors.leftMargin: 20
                        anchors.rightMargin: 20
                        anchors.topMargin: 30
                        anchors.bottomMargin: 20

                        ListView {
                            id: chatListView

                            spacing: 25
                            topMargin: 20
                            bottomMargin: 20

                            delegate: ChatMessage {
                                isUserMessage: model.isUser
                                messageText: model.message
                                timeStamp: model.timestamp
                                width: chatListView.width
                            }
                            model: ListModel {
                                id: chatModel

                                Component.onCompleted: {
                                    // 添加一些示例消息
                                    append({
                                        "isUser": false,
                                        "message": "你好，我是DeepSeek，你的AI助手。我可以帮助你解答问题、编写代码、分析文档等。有什么我可以帮助你的吗？",
                                        "timestamp": "刚刚"
                                    });

                                    append({
                                        "isUser": true,
                                        "message": "你能帮我写一个Python的快速排序算法吗？",
                                        "timestamp": "10:30"
                                    });

                                    append({
                                        "isUser": false,
                                        "message": "当然可以！下面是一个Python快速排序的实现：\n\n```python\ndef quicksort(arr):\n    if len(arr) <= 1:\n        return arr\n    \n    pivot = arr[len(arr) // 2]\n    left = [x for x in arr if x < pivot]\n    middle = [x for x in arr if x == pivot]\n    right = [x for x in arr if x > pivot]\n    \n    return quicksort(left) + middle + quicksort(right)\n\n# 使用示例\narr = [3, 6, 8, 10, 1, 2, 1]\nsorted_arr = quicksort(arr)\nprint(sorted_arr)  # 输出: [1, 1, 2, 3, 6, 8, 10]\n```\n\n**算法说明：**\n- 选择数组中间的元素作为基准点（pivot）\n- 将数组分为三部分：小于基准、等于基准、大于基准\n- 递归地对左右两部分进行排序\n- 合并结果\n\n时间复杂度：平均 O(n log n)，最坏 O(n²)",
                                        "timestamp": "10:31"
                                    });
                                }
                            }
                        }
                    }
                }

                // 底部输入区域
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 80
                    border.color: "#e0e0e0"
                    border.width: 1
                    color: "white"

                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 15
                        spacing: 10

                        // 输入框
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 50
                            border.color: "#e0e0e0"
                            border.width: 1
                            color: "white"
                            radius: 8

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 10
                                spacing: 10

                                ScrollView {
                                    Layout.fillHeight: true
                                    Layout.fillWidth: true

                                    TextArea {
                                        id: messageInput

                                        font.pixelSize: 14
                                        placeholderText: "请输入你的问题，按 Enter 发送"
                                        selectByMouse: true
                                        wrapMode: TextArea.Wrap

                                        background: Rectangle {
                                            color: "transparent"
                                        }

                                        Keys.onPressed: function (event) {
                                            if (event.key === Qt.Key_Return && !(event.modifiers & Qt.ShiftModifier)) {
                                                sendMessage();
                                                event.accepted = true;
                                            }
                                        }
                                    }
                                }

                                // 底部工具栏
                                RowLayout {
                                    Layout.alignment: Qt.AlignBottom
                                    spacing: 5

                                    Button {
                                        Layout.preferredHeight: 30
                                        Layout.preferredWidth: 30
                                        flat: true
                                        font.pixelSize: 16
                                        text: "📎"
                                    }
                                    Button {
                                        Layout.preferredHeight: 30
                                        Layout.preferredWidth: 30
                                        flat: true
                                        font.pixelSize: 16
                                        text: "📷"
                                    }
                                    Button {
                                        Layout.preferredHeight: 30
                                        Layout.preferredWidth: 30
                                        flat: true
                                        font.pixelSize: 16
                                        text: "🎤"
                                    }
                                    Button {
                                        Layout.preferredHeight: 30
                                        Layout.preferredWidth: 30
                                        flat: true
                                        font.pixelSize: 16
                                        text: "📋"
                                    }
                                    Button {
                                        Layout.preferredHeight: 30
                                        Layout.preferredWidth: 30
                                        flat: true
                                        font.pixelSize: 16
                                        text: "🎯"
                                    }
                                    Button {
                                        Layout.preferredHeight: 30
                                        Layout.preferredWidth: 30
                                        flat: true
                                        font.pixelSize: 16
                                        text: "😊"
                                    }
                                }
                            }
                        }

                        // 发送按钮
                        Button {
                            Layout.preferredHeight: 50
                            Layout.preferredWidth: 60
                            font.pixelSize: 18
                            text: "✈"

                            background: Rectangle {
                                color: parent.pressed ? "#45a049" : "#4CAF50"
                                radius: 8
                            }
                            contentItem: Text {
                                color: "white"
                                font: parent.font
                                horizontalAlignment: Text.AlignHCenter
                                text: parent.text
                                verticalAlignment: Text.AlignVCenter
                            }

                            onClicked: sendMessage()
                        }
                    }
                }
            }
        }
    }
}
